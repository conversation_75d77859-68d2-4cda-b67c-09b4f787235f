# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32\build

# Include any dependencies generated for this target.
include CMakeFiles/animal.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/animal.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/animal.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/animal.dir/flags.make

CMakeFiles/animal.dir/codegen:
.PHONY : CMakeFiles/animal.dir/codegen

CMakeFiles/animal.dir/dog.cpp.obj: CMakeFiles/animal.dir/flags.make
CMakeFiles/animal.dir/dog.cpp.obj: D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/dog.cpp
CMakeFiles/animal.dir/dog.cpp.obj: CMakeFiles/animal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/animal.dir/dog.cpp.obj"
	D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/animal.dir/dog.cpp.obj -MF CMakeFiles\animal.dir\dog.cpp.obj.d -o CMakeFiles\animal.dir\dog.cpp.obj -c D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32\dog.cpp

CMakeFiles/animal.dir/dog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/animal.dir/dog.cpp.i"
	D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32\dog.cpp > CMakeFiles\animal.dir\dog.cpp.i

CMakeFiles/animal.dir/dog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/animal.dir/dog.cpp.s"
	D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32\dog.cpp -o CMakeFiles\animal.dir\dog.cpp.s

# Object files for target animal
animal_OBJECTS = \
"CMakeFiles/animal.dir/dog.cpp.obj"

# External object files for target animal
animal_EXTERNAL_OBJECTS =

animal.exe: CMakeFiles/animal.dir/dog.cpp.obj
animal.exe: CMakeFiles/animal.dir/build.make
animal.exe: CMakeFiles/animal.dir/linkLibs.rsp
animal.exe: CMakeFiles/animal.dir/objects1.rsp
animal.exe: CMakeFiles/animal.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable animal.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\animal.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/animal.dir/build: animal.exe
.PHONY : CMakeFiles/animal.dir/build

CMakeFiles/animal.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\animal.dir\cmake_clean.cmake
.PHONY : CMakeFiles/animal.dir/clean

CMakeFiles/animal.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32 D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32 D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32\build D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32\build D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_32\build\CMakeFiles\animal.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/animal.dir/depend

