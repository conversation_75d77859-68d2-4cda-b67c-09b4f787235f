
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/8/20 9:43:53。
      节点 1 上的项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate”执行 Touch 任务。
      已完成生成项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:00.82
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/3.31.5/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/8/20 9:43:54。
      节点 1 上的项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:00.65
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/3.31.5/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-kla9dx"
      binary: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-kla9dx"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-kla9dx'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/MSBuild/Current/Bin/MSBuild.exe" cmTC_626de.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/8/20 9:43:55。
        节点 1 上的项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kla9dx\\cmTC_626de.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_626de.dir\\Debug\\”。
          正在创建目录“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kla9dx\\Debug\\”。
          正在创建目录“cmTC_626de.dir\\Debug\\cmTC_626de.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_626de.dir\\Debug\\cmTC_626de.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_626de.dir\\Debug\\\\" /Fd"cmTC_626de.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          CMakeCCompilerABI.c
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_626de.dir\\Debug\\\\" /Fd"cmTC_626de.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kla9dx\\Debug\\cmTC_626de.exe" /INCREMENTAL /ILK:"cmTC_626de.dir\\Debug\\cmTC_626de.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-kla9dx/Debug/cmTC_626de.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-kla9dx/Debug/cmTC_626de.lib" /MACHINE:X64  /machine:x64 cmTC_626de.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_626de.vcxproj -> D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kla9dx\\Debug\\cmTC_626de.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_626de.dir\\Debug\\cmTC_626de.tlog\\unsuccessfulbuild”。
          正在对“cmTC_626de.dir\\Debug\\cmTC_626de.tlog\\cmTC_626de.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kla9dx\\cmTC_626de.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:00.63
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-w48qqd"
      binary: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-w48qqd"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-w48qqd'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/MSBuild/Current/Bin/MSBuild.exe" cmTC_57bbd.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/8/20 9:43:56。
        节点 1 上的项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w48qqd\\cmTC_57bbd.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_57bbd.dir\\Debug\\”。
          正在创建目录“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w48qqd\\Debug\\”。
          正在创建目录“cmTC_57bbd.dir\\Debug\\cmTC_57bbd.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_57bbd.dir\\Debug\\cmTC_57bbd.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_57bbd.dir\\Debug\\\\" /Fd"cmTC_57bbd.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          CMakeCXXCompilerABI.cpp
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_57bbd.dir\\Debug\\\\" /Fd"cmTC_57bbd.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
        C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp : fatal error C1090: PDB API 调用失败，错误代码“3”: D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w48qqd\\cmTC_57bbd.dir\\Debug\\vc142.pdb [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w48qqd\\cmTC_57bbd.vcxproj]
        已完成生成项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w48qqd\\cmTC_57bbd.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w48qqd\\cmTC_57bbd.vcxproj”(默认目标) (1) ->
        (ClCompile 目标) -> 
          C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp : fatal error C1090: PDB API 调用失败，错误代码“3”: D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w48qqd\\cmTC_57bbd.dir\\Debug\\vc142.pdb [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-w48qqd\\cmTC_57bbd.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.43
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working CXX compiler: C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe"
    directories:
      source: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-1f7gyq"
      binary: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-1f7gyq"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-1f7gyq'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/MSBuild/Current/Bin/MSBuild.exe" cmTC_f2bb3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/8/20 9:43:57。
        节点 1 上的项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1f7gyq\\cmTC_f2bb3.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_f2bb3.dir\\Debug\\”。
          正在创建目录“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1f7gyq\\Debug\\”。
          正在创建目录“cmTC_f2bb3.dir\\Debug\\cmTC_f2bb3.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_f2bb3.dir\\Debug\\cmTC_f2bb3.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_f2bb3.dir\\Debug\\\\" /Fd"cmTC_f2bb3.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1f7gyq\\testCXXCompiler.cxx"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          testCXXCompiler.cxx
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_f2bb3.dir\\Debug\\\\" /Fd"cmTC_f2bb3.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1f7gyq\\testCXXCompiler.cxx"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1f7gyq\\Debug\\cmTC_f2bb3.exe" /INCREMENTAL /ILK:"cmTC_f2bb3.dir\\Debug\\cmTC_f2bb3.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-1f7gyq/Debug/cmTC_f2bb3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_32/build/CMakeFiles/CMakeScratch/TryCompile-1f7gyq/Debug/cmTC_f2bb3.lib" /MACHINE:X64  /machine:x64 cmTC_f2bb3.dir\\Debug\\testCXXCompiler.obj
          cmTC_f2bb3.vcxproj -> D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1f7gyq\\Debug\\cmTC_f2bb3.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_f2bb3.dir\\Debug\\cmTC_f2bb3.tlog\\unsuccessfulbuild”。
          正在对“cmTC_f2bb3.dir\\Debug\\cmTC_f2bb3.tlog\\cmTC_f2bb3.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_32\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1f7gyq\\cmTC_f2bb3.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:00.73
        
      exitCode: 0
...
