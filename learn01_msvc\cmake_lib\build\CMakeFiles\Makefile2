# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/hello.dir/all
all: animal/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/hello.dir/codegen
codegen: animal/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: animal/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/hello.dir/clean
clean: animal/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory animal

# Recursive "all" directory target.
animal/all: animal/CMakeFiles/animalLib.dir/all
.PHONY : animal/all

# Recursive "codegen" directory target.
animal/codegen: animal/CMakeFiles/animalLib.dir/codegen
.PHONY : animal/codegen

# Recursive "preinstall" directory target.
animal/preinstall:
.PHONY : animal/preinstall

# Recursive "clean" directory target.
animal/clean: animal/CMakeFiles/animalLib.dir/clean
.PHONY : animal/clean

#=============================================================================
# Target rules for target CMakeFiles/hello.dir

# All Build rule for target.
CMakeFiles/hello.dir/all: animal/CMakeFiles/animalLib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\hello.dir\build.make CMakeFiles/hello.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\hello.dir\build.make CMakeFiles/hello.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=4,5 "Built target hello"
.PHONY : CMakeFiles/hello.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/hello.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/hello.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles 0
.PHONY : CMakeFiles/hello.dir/rule

# Convenience name for target.
hello: CMakeFiles/hello.dir/rule
.PHONY : hello

# codegen rule for target.
CMakeFiles/hello.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\hello.dir\build.make CMakeFiles/hello.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=4,5 "Finished codegen for target hello"
.PHONY : CMakeFiles/hello.dir/codegen

# clean rule for target.
CMakeFiles/hello.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\hello.dir\build.make CMakeFiles/hello.dir/clean
.PHONY : CMakeFiles/hello.dir/clean

#=============================================================================
# Target rules for target animal/CMakeFiles/animalLib.dir

# All Build rule for target.
animal/CMakeFiles/animalLib.dir/all:
	$(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/depend
	$(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=1,2,3 "Built target animalLib"
.PHONY : animal/CMakeFiles/animalLib.dir/all

# Build rule for subdir invocation for target.
animal/CMakeFiles/animalLib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 animal/CMakeFiles/animalLib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles 0
.PHONY : animal/CMakeFiles/animalLib.dir/rule

# Convenience name for target.
animalLib: animal/CMakeFiles/animalLib.dir/rule
.PHONY : animalLib

# codegen rule for target.
animal/CMakeFiles/animalLib.dir/codegen:
	$(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=1,2,3 "Finished codegen for target animalLib"
.PHONY : animal/CMakeFiles/animalLib.dir/codegen

# clean rule for target.
animal/CMakeFiles/animalLib.dir/clean:
	$(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/clean
.PHONY : animal/CMakeFiles/animalLib.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

