
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/c++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/3.31.5/CompilerIdCXX/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-n5eotb"
      binary: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-n5eotb"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-n5eotb'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/mingw32-make.exe -f Makefile cmTC_8f5e6/fast
        D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_8f5e6.dir\\build.make CMakeFiles/cmTC_8f5e6.dir/build
        mingw32-make.exe[1]: Entering directory 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-n5eotb'
        Building CXX object CMakeFiles/cmTC_8f5e6.dir/CMakeCXXCompilerABI.cpp.obj
        D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe   -v -o CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
        Using built-in specs.
        COLLECT_GCC=D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe
        Target: i686-w64-mingw32
        Configured with: ../../../src/gcc-4.9.2/configure --host=i686-w64-mingw32 --build=i686-w64-mingw32 --target=i686-w64-mingw32 --prefix=/mingw32 --with-sysroot=/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32 --with-gxx-include-dir=/mingw32/i686-w64-mingw32/include/c++ --enable-shared --enable-static --disable-multilib --enable-languages=ada,c,c++,fortran,objc,obj-c++,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-sjlj-exceptions --with-dwarf2 --disable-isl-version-check --disable-cloog-version-check --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=i686 --with-tune=generic --with-libiconv --with-system-zlib --with-gmp=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-mpfr=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-mpc=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-isl=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-cloog=/c/mingw492/prerequisites/i686-w64-mingw32-static --enable-cloog-backend=isl --with-pkgversion='i686-posix-dwarf-rev1, Built by MinGW-W64 project' --with-bugurl=http://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -I/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/include -I/c/mingw492/prerequisites/i686-zlib-static/include -I/c/mingw492/prerequisites/i686-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -I/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/include -I/c/mingw492/prerequisites/i686-zlib-static/include -I/c/mingw492/prerequisites/i686-w64-mingw32-static/include' CPPFLAGS= LDFLAGS='-pipe -L/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/lib -L/c/mingw492/prerequisites/i686-zlib-static/lib -L/c/mingw492/prerequisites/i686-w64-mingw32-static/lib -Wl,--large-address-aware'
        Thread model: posix
        gcc version 4.9.2 (i686-posix-dwarf-rev1, Built by MinGW-W64 project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i686'
         D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/cc1plus.exe -quiet -v -iprefix D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=i686 -auxbase-strip CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cceJLRka.s
        GNU C++ (i686-posix-dwarf-rev1, Built by MinGW-W64 project) version 4.9.2 (i686-w64-mingw32)
        	compiled by GNU C version 4.9.2, GMP version 6.0.0, MPFR version 3.1.2-p9, MPC version 1.0.2
        warning: MPFR header version 3.1.2-p9 differs from library version 3.1.2-p10.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../lib/gcc/i686-w64-mingw32/4.9.2/include"
        ignoring nonexistent directory "C:/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32C:/msys64/mingw32/lib/gcc/i686-w64-mingw32/4.9.2/../../../../include"
        ignoring duplicate directory "D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../lib/gcc/i686-w64-mingw32/4.9.2/include-fixed"
        ignoring duplicate directory "D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/include"
        ignoring nonexistent directory "C:/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include
         D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include-fixed
         D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/include
         D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++
         D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++/i686-w64-mingw32
         D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++/backward
        End of search list.
        GNU C++ (i686-posix-dwarf-rev1, Built by MinGW-W64 project) version 4.9.2 (i686-w64-mingw32)
        	compiled by GNU C version 4.9.2, GMP version 6.0.0, MPFR version 3.1.2-p9, MPC version 1.0.2
        warning: MPFR header version 3.1.2-p9 differs from library version 3.1.2-p10.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 0d25aaaf6874cf89759175bf016c1454
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i686'
         D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cceJLRka.s
        GNU assembler version 2.24 (i686-w64-mingw32) using BFD version (GNU Binutils) 2.24
        COMPILER_PATH=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/bin/
        LIBRARY_PATH=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i686'
        Linking CXX executable cmTC_8f5e6.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_8f5e6.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_8f5e6.dir/objects.a
        D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\ar.exe qc CMakeFiles\\cmTC_8f5e6.dir/objects.a @CMakeFiles\\cmTC_8f5e6.dir\\objects1.rsp
        D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_8f5e6.dir/objects.a -Wl,--no-whole-archive -o cmTC_8f5e6.exe -Wl,--out-implib,libcmTC_8f5e6.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe
        COLLECT_LTO_WRAPPER=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/lto-wrapper.exe
        Target: i686-w64-mingw32
        Configured with: ../../../src/gcc-4.9.2/configure --host=i686-w64-mingw32 --build=i686-w64-mingw32 --target=i686-w64-mingw32 --prefix=/mingw32 --with-sysroot=/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32 --with-gxx-include-dir=/mingw32/i686-w64-mingw32/include/c++ --enable-shared --enable-static --disable-multilib --enable-languages=ada,c,c++,fortran,objc,obj-c++,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-sjlj-exceptions --with-dwarf2 --disable-isl-version-check --disable-cloog-version-check --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=i686 --with-tune=generic --with-libiconv --with-system-zlib --with-gmp=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-mpfr=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-mpc=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-isl=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-cloog=/c/mingw492/prerequisites/i686-w64-mingw32-static --enable-cloog-backend=isl --with-pkgversion='i686-posix-dwarf-rev1, Built by MinGW-W64 project' --with-bugurl=http://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -I/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/include -I/c/mingw492/prerequisites/i686-zlib-static/include -I/c/mingw492/prerequisites/i686-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -I/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/include -I/c/mingw492/prerequisites/i686-zlib-static/include -I/c/mingw492/prerequisites/i686-w64-mingw32-static/include' CPPFLAGS= LDFLAGS='-pipe -L/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/lib -L/c/mingw492/prerequisites/i686-zlib-static/lib -L/c/mingw492/prerequisites/i686-w64-mingw32-static/lib -Wl,--large-address-aware'
        Thread model: posix
        gcc version 4.9.2 (i686-posix-dwarf-rev1, Built by MinGW-W64 project) 
        COMPILER_PATH=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/bin/
        LIBRARY_PATH=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/;D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8f5e6.exe' '-shared-libgcc' '-mtune=generic' '-march=i686'
         D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/collect2.exe -plugin D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/liblto_plugin-0.dll -plugin-opt=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccU6MYhk.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32 -m i386pe -Bdynamic -u ___register_frame_info -u ___deregister_frame_info -o cmTC_8f5e6.exe D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/crt2.o D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtbegin.o -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2 -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../.. -v --whole-archive CMakeFiles\\cmTC_8f5e6.dir/objects.a --no-whole-archive --out-implib libcmTC_8f5e6.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtend.o
        collect2 version 4.9.2
        D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/bin/ld.exe -plugin D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/liblto_plugin-0.dll -plugin-opt=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccU6MYhk.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32 -m i386pe -Bdynamic -u ___register_frame_info -u ___deregister_frame_info -o cmTC_8f5e6.exe D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/crt2.o D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtbegin.o -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2 -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../.. -v --whole-archive CMakeFiles\\cmTC_8f5e6.dir/objects.a --no-whole-archive --out-implib libcmTC_8f5e6.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtend.o
        GNU ld (GNU Binutils) 2.24
        mingw32-make.exe[1]: Leaving directory 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-n5eotb'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include]
          add: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include-fixed]
          add: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/include]
          add: [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++]
          add: [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++/i686-w64-mingw32]
          add: [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++/backward]
        end of search list found
        collapse include dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2/include]
        collapse include dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include-fixed] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed]
        collapse include dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/include] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/include]
        collapse include dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/include/c++]
        collapse include dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++/i686-w64-mingw32] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/include/c++/i686-w64-mingw32]
        collapse include dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++/backward] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/include/c++/backward]
        implicit include dirs: [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2/include;D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed;D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/include;D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/include/c++;D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/include/c++/i686-w64-mingw32;D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/include/c++/backward]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-n5eotb']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/mingw32-make.exe -f Makefile cmTC_8f5e6/fast]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_8f5e6.dir\\build.make CMakeFiles/cmTC_8f5e6.dir/build]
        ignore line: [mingw32-make.exe[1]: Entering directory 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-n5eotb']
        ignore line: [Building CXX object CMakeFiles/cmTC_8f5e6.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe   -v -o CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe]
        ignore line: [Target: i686-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-4.9.2/configure --host=i686-w64-mingw32 --build=i686-w64-mingw32 --target=i686-w64-mingw32 --prefix=/mingw32 --with-sysroot=/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32 --with-gxx-include-dir=/mingw32/i686-w64-mingw32/include/c++ --enable-shared --enable-static --disable-multilib --enable-languages=ada,c,c++,fortran,objc,obj-c++,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-sjlj-exceptions --with-dwarf2 --disable-isl-version-check --disable-cloog-version-check --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=i686 --with-tune=generic --with-libiconv --with-system-zlib --with-gmp=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-mpfr=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-mpc=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-isl=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-cloog=/c/mingw492/prerequisites/i686-w64-mingw32-static --enable-cloog-backend=isl --with-pkgversion='i686-posix-dwarf-rev1, Built by MinGW-W64 project' --with-bugurl=http://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -I/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/include -I/c/mingw492/prerequisites/i686-zlib-static/include -I/c/mingw492/prerequisites/i686-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -I/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/include -I/c/mingw492/prerequisites/i686-zlib-static/include -I/c/mingw492/prerequisites/i686-w64-mingw32-static/include' CPPFLAGS= LDFLAGS='-pipe -L/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/lib -L/c/mingw492/prerequisites/i686-zlib-static/lib -L/c/mingw492/prerequisites/i686-w64-mingw32-static/lib -Wl,--large-address-aware']
        ignore line: [Thread model: posix]
        ignore line: [gcc version 4.9.2 (i686-posix-dwarf-rev1  Built by MinGW-W64 project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i686']
        ignore line: [ D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/cc1plus.exe -quiet -v -iprefix D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=i686 -auxbase-strip CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cceJLRka.s]
        ignore line: [GNU C++ (i686-posix-dwarf-rev1  Built by MinGW-W64 project) version 4.9.2 (i686-w64-mingw32)]
        ignore line: [	compiled by GNU C version 4.9.2  GMP version 6.0.0  MPFR version 3.1.2-p9  MPC version 1.0.2]
        ignore line: [warning: MPFR header version 3.1.2-p9 differs from library version 3.1.2-p10.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../lib/gcc/i686-w64-mingw32/4.9.2/include"]
        ignore line: [ignoring nonexistent directory "C:/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32C:/msys64/mingw32/lib/gcc/i686-w64-mingw32/4.9.2/../../../../include"]
        ignore line: [ignoring duplicate directory "D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../lib/gcc/i686-w64-mingw32/4.9.2/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "C:/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include]
        ignore line: [ D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include-fixed]
        ignore line: [ D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/include]
        ignore line: [ D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++]
        ignore line: [ D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++/i686-w64-mingw32]
        ignore line: [ D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/../../i686-w64-mingw32/include/c++/backward]
        ignore line: [End of search list.]
        ignore line: [GNU C++ (i686-posix-dwarf-rev1  Built by MinGW-W64 project) version 4.9.2 (i686-w64-mingw32)]
        ignore line: [	compiled by GNU C version 4.9.2  GMP version 6.0.0  MPFR version 3.1.2-p9  MPC version 1.0.2]
        ignore line: [warning: MPFR header version 3.1.2-p9 differs from library version 3.1.2-p10.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 0d25aaaf6874cf89759175bf016c1454]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i686']
        ignore line: [ D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cceJLRka.s]
        ignore line: [GNU assembler version 2.24 (i686-w64-mingw32) using BFD version (GNU Binutils) 2.24]
        ignore line: [COMPILER_PATH=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_8f5e6.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i686']
        ignore line: [Linking CXX executable cmTC_8f5e6.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_8f5e6.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_8f5e6.dir/objects.a]
        ignore line: [D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\ar.exe qc CMakeFiles\\cmTC_8f5e6.dir/objects.a @CMakeFiles\\cmTC_8f5e6.dir\\objects1.rsp]
        ignore line: [D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe  -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_8f5e6.dir/objects.a -Wl --no-whole-archive -o cmTC_8f5e6.exe -Wl --out-implib libcmTC_8f5e6.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/lto-wrapper.exe]
        ignore line: [Target: i686-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-4.9.2/configure --host=i686-w64-mingw32 --build=i686-w64-mingw32 --target=i686-w64-mingw32 --prefix=/mingw32 --with-sysroot=/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32 --with-gxx-include-dir=/mingw32/i686-w64-mingw32/include/c++ --enable-shared --enable-static --disable-multilib --enable-languages=ada,c,c++,fortran,objc,obj-c++,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-sjlj-exceptions --with-dwarf2 --disable-isl-version-check --disable-cloog-version-check --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=i686 --with-tune=generic --with-libiconv --with-system-zlib --with-gmp=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-mpfr=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-mpc=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-isl=/c/mingw492/prerequisites/i686-w64-mingw32-static --with-cloog=/c/mingw492/prerequisites/i686-w64-mingw32-static --enable-cloog-backend=isl --with-pkgversion='i686-posix-dwarf-rev1, Built by MinGW-W64 project' --with-bugurl=http://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -I/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/include -I/c/mingw492/prerequisites/i686-zlib-static/include -I/c/mingw492/prerequisites/i686-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -I/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/include -I/c/mingw492/prerequisites/i686-zlib-static/include -I/c/mingw492/prerequisites/i686-w64-mingw32-static/include' CPPFLAGS= LDFLAGS='-pipe -L/c/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32/opt/lib -L/c/mingw492/prerequisites/i686-zlib-static/lib -L/c/mingw492/prerequisites/i686-w64-mingw32-static/lib -Wl,--large-address-aware']
        ignore line: [Thread model: posix]
        ignore line: [gcc version 4.9.2 (i686-posix-dwarf-rev1  Built by MinGW-W64 project) ]
        ignore line: [COMPILER_PATH=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_8f5e6.exe' '-shared-libgcc' '-mtune=generic' '-march=i686']
        link line: [ D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/collect2.exe -plugin D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/liblto_plugin-0.dll -plugin-opt=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccU6MYhk.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32 -m i386pe -Bdynamic -u ___register_frame_info -u ___deregister_frame_info -o cmTC_8f5e6.exe D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/crt2.o D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtbegin.o -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2 -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../.. -v --whole-archive CMakeFiles\\cmTC_8f5e6.dir/objects.a --no-whole-archive --out-implib libcmTC_8f5e6.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtend.o]
          arg [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccU6MYhk.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-liconv] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [--sysroot=C:/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pe] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-u] ==> ignore
          arg [___register_frame_info] ==> ignore
          arg [-u] ==> ignore
          arg [___deregister_frame_info] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_8f5e6.exe] ==> ignore
          arg [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/crt2.o] ==> obj [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/crt2.o]
          arg [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtbegin.o] ==> obj [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtbegin.o]
          arg [-LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2] ==> dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2]
          arg [-LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc] ==> dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc]
          arg [-LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib] ==> dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib]
          arg [-LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib] ==> dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib]
          arg [-LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib] ==> dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib]
          arg [-LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../..] ==> dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_8f5e6.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_8f5e6.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtend.o] ==> obj [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtend.o]
        ignore line: [collect2 version 4.9.2]
        ignore line: [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/bin/ld.exe -plugin D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/liblto_plugin-0.dll -plugin-opt=D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../libexec/gcc/i686-w64-mingw32/4.9.2/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccU6MYhk.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw492/i686-492-posix-dwarf-rt_v3-rev1/mingw32 -m i386pe -Bdynamic -u ___register_frame_info -u ___deregister_frame_info -o cmTC_8f5e6.exe D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/crt2.o D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtbegin.o -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2 -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib -LD:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../.. -v --whole-archive CMakeFiles\\cmTC_8f5e6.dir/objects.a --no-whole-archive --out-implib libcmTC_8f5e6.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtend.o]
        linker tool for 'CXX': D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib/crt2.o] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/lib/crt2.o]
        collapse obj [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtbegin.o] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2/crtbegin.o]
        collapse obj [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/crtend.o] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2/crtend.o]
        collapse library dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2]
        collapse library dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc]
        collapse library dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib/../lib] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/lib]
        collapse library dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../lib] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib]
        collapse library dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/lib] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/lib]
        collapse library dir [D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../..] ==> [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;moldname;mingwex;pthread;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc_s;gcc;moldname;mingwex]
        implicit objs: [D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/lib/crt2.o;D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2/crtbegin.o;D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2/crtend.o]
        implicit dirs: [D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc/i686-w64-mingw32/4.9.2;D:/Qt/Qt5.6.3/Tools/mingw492_32/lib/gcc;D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/lib;D:/Qt/Qt5.6.3/Tools/mingw492_32/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "D:/Qt/Qt5.6.3/Tools/mingw492_32/i686-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.24
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/FeatureTesting.cmake:87 (_record_compiler_features)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake:124 (_record_compiler_features_cxx)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerSupport.cmake:70 (cmake_record_cxx_compile_features)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:83 (CMAKE_DETERMINE_COMPILER_SUPPORT)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-biygtl"
      binary: "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-biygtl"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-biygtl'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/mingw32-make.exe -f Makefile cmTC_963ff/fast
        D:/Qt/Qt5.6.3/Tools/mingw492_32/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_963ff.dir\\build.make CMakeFiles/cmTC_963ff.dir/build
        mingw32-make.exe[1]: Entering directory 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-biygtl'
        Building CXX object CMakeFiles/cmTC_963ff.dir/feature_tests.cxx.obj
        D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe   -std=c++14 -o CMakeFiles\\cmTC_963ff.dir\\feature_tests.cxx.obj -c D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\cmake_lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-biygtl\\feature_tests.cxx
        Linking CXX executable cmTC_963ff.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_963ff.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_963ff.dir/objects.a
        D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\ar.exe qc CMakeFiles\\cmTC_963ff.dir/objects.a @CMakeFiles\\cmTC_963ff.dir\\objects1.rsp
        D:\\Qt\\Qt5.6.3\\Tools\\mingw492_32\\bin\\c++.exe -Wl,--whole-archive CMakeFiles\\cmTC_963ff.dir/objects.a -Wl,--no-whole-archive -o cmTC_963ff.exe -Wl,--out-implib,libcmTC_963ff.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_963ff.dir\\linkLibs.rsp
        mingw32-make.exe[1]: Leaving directory 'D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/CMakeScratch/TryCompile-biygtl'
        
      exitCode: 0
...
