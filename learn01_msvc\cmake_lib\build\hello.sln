﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{1E63A314-B5F1-3A8E-842D-FD614F3D03A6}"
	ProjectSection(ProjectDependencies) = postProject
		{0780390A-C3D4-304F-8891-7D67B727E1C3} = {0780390A-C3D4-304F-8891-7D67B727E1C3}
		{39D905AF-F8B1-34BC-8EB9-B027944BF570} = {39D905AF-F8B1-34BC-8EB9-B027944BF570}
		{6CE0CF52-AD75-3383-890F-928C11B7E3AD} = {6CE0CF52-AD75-3383-890F-928C11B7E3AD}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{0780390A-C3D4-304F-8891-7D67B727E1C3}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "animal", "animal\animal.vcxproj", "{39D905AF-F8B1-34BC-8EB9-B027944BF570}"
	ProjectSection(ProjectDependencies) = postProject
		{0780390A-C3D4-304F-8891-7D67B727E1C3} = {0780390A-C3D4-304F-8891-7D67B727E1C3}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hello", "hello.vcxproj", "{6CE0CF52-AD75-3383-890F-928C11B7E3AD}"
	ProjectSection(ProjectDependencies) = postProject
		{0780390A-C3D4-304F-8891-7D67B727E1C3} = {0780390A-C3D4-304F-8891-7D67B727E1C3}
		{39D905AF-F8B1-34BC-8EB9-B027944BF570} = {39D905AF-F8B1-34BC-8EB9-B027944BF570}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1E63A314-B5F1-3A8E-842D-FD614F3D03A6}.Debug|x64.ActiveCfg = Debug|x64
		{1E63A314-B5F1-3A8E-842D-FD614F3D03A6}.Release|x64.ActiveCfg = Release|x64
		{1E63A314-B5F1-3A8E-842D-FD614F3D03A6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{1E63A314-B5F1-3A8E-842D-FD614F3D03A6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{0780390A-C3D4-304F-8891-7D67B727E1C3}.Debug|x64.ActiveCfg = Debug|x64
		{0780390A-C3D4-304F-8891-7D67B727E1C3}.Debug|x64.Build.0 = Debug|x64
		{0780390A-C3D4-304F-8891-7D67B727E1C3}.Release|x64.ActiveCfg = Release|x64
		{0780390A-C3D4-304F-8891-7D67B727E1C3}.Release|x64.Build.0 = Release|x64
		{0780390A-C3D4-304F-8891-7D67B727E1C3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0780390A-C3D4-304F-8891-7D67B727E1C3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{0780390A-C3D4-304F-8891-7D67B727E1C3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{0780390A-C3D4-304F-8891-7D67B727E1C3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{39D905AF-F8B1-34BC-8EB9-B027944BF570}.Debug|x64.ActiveCfg = Debug|x64
		{39D905AF-F8B1-34BC-8EB9-B027944BF570}.Debug|x64.Build.0 = Debug|x64
		{39D905AF-F8B1-34BC-8EB9-B027944BF570}.Release|x64.ActiveCfg = Release|x64
		{39D905AF-F8B1-34BC-8EB9-B027944BF570}.Release|x64.Build.0 = Release|x64
		{39D905AF-F8B1-34BC-8EB9-B027944BF570}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{39D905AF-F8B1-34BC-8EB9-B027944BF570}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{39D905AF-F8B1-34BC-8EB9-B027944BF570}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{39D905AF-F8B1-34BC-8EB9-B027944BF570}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{6CE0CF52-AD75-3383-890F-928C11B7E3AD}.Debug|x64.ActiveCfg = Debug|x64
		{6CE0CF52-AD75-3383-890F-928C11B7E3AD}.Debug|x64.Build.0 = Debug|x64
		{6CE0CF52-AD75-3383-890F-928C11B7E3AD}.Release|x64.ActiveCfg = Release|x64
		{6CE0CF52-AD75-3383-890F-928C11B7E3AD}.Release|x64.Build.0 = Release|x64
		{6CE0CF52-AD75-3383-890F-928C11B7E3AD}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6CE0CF52-AD75-3383-890F-928C11B7E3AD}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6CE0CF52-AD75-3383-890F-928C11B7E3AD}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6CE0CF52-AD75-3383-890F-928C11B7E3AD}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E70FE620-837B-3F8B-9059-9EFD4622187C}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
