# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build

# Include any dependencies generated for this target.
include animal/CMakeFiles/animalLib.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include animal/CMakeFiles/animalLib.dir/compiler_depend.make

# Include the progress variables for this target.
include animal/CMakeFiles/animalLib.dir/progress.make

# Include the compile flags for this target's objects.
include animal/CMakeFiles/animalLib.dir/flags.make

animal/CMakeFiles/animalLib.dir/codegen:
.PHONY : animal/CMakeFiles/animalLib.dir/codegen

animal/CMakeFiles/animalLib.dir/dog.cpp.obj: animal/CMakeFiles/animalLib.dir/flags.make
animal/CMakeFiles/animalLib.dir/dog.cpp.obj: D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/animal/dog.cpp
animal/CMakeFiles/animalLib.dir/dog.cpp.obj: animal/CMakeFiles/animalLib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object animal/CMakeFiles/animalLib.dir/dog.cpp.obj"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT animal/CMakeFiles/animalLib.dir/dog.cpp.obj -MF CMakeFiles\animalLib.dir\dog.cpp.obj.d -o CMakeFiles\animalLib.dir\dog.cpp.obj -c D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\dog.cpp

animal/CMakeFiles/animalLib.dir/dog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/animalLib.dir/dog.cpp.i"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\dog.cpp > CMakeFiles\animalLib.dir\dog.cpp.i

animal/CMakeFiles/animalLib.dir/dog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/animalLib.dir/dog.cpp.s"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\dog.cpp -o CMakeFiles\animalLib.dir\dog.cpp.s

animal/CMakeFiles/animalLib.dir/cat.cpp.obj: animal/CMakeFiles/animalLib.dir/flags.make
animal/CMakeFiles/animalLib.dir/cat.cpp.obj: D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/animal/cat.cpp
animal/CMakeFiles/animalLib.dir/cat.cpp.obj: animal/CMakeFiles/animalLib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object animal/CMakeFiles/animalLib.dir/cat.cpp.obj"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT animal/CMakeFiles/animalLib.dir/cat.cpp.obj -MF CMakeFiles\animalLib.dir\cat.cpp.obj.d -o CMakeFiles\animalLib.dir\cat.cpp.obj -c D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\cat.cpp

animal/CMakeFiles/animalLib.dir/cat.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/animalLib.dir/cat.cpp.i"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\cat.cpp > CMakeFiles\animalLib.dir\cat.cpp.i

animal/CMakeFiles/animalLib.dir/cat.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/animalLib.dir/cat.cpp.s"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\cat.cpp -o CMakeFiles\animalLib.dir\cat.cpp.s

# Object files for target animalLib
animalLib_OBJECTS = \
"CMakeFiles/animalLib.dir/dog.cpp.obj" \
"CMakeFiles/animalLib.dir/cat.cpp.obj"

# External object files for target animalLib
animalLib_EXTERNAL_OBJECTS =

animal/libanimalLib.a: animal/CMakeFiles/animalLib.dir/dog.cpp.obj
animal/libanimalLib.a: animal/CMakeFiles/animalLib.dir/cat.cpp.obj
animal/libanimalLib.a: animal/CMakeFiles/animalLib.dir/build.make
animal/libanimalLib.a: animal/CMakeFiles/animalLib.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libanimalLib.a"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && $(CMAKE_COMMAND) -P CMakeFiles\animalLib.dir\cmake_clean_target.cmake
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\animalLib.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
animal/CMakeFiles/animalLib.dir/build: animal/libanimalLib.a
.PHONY : animal/CMakeFiles/animalLib.dir/build

animal/CMakeFiles/animalLib.dir/clean:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && $(CMAKE_COMMAND) -P CMakeFiles\animalLib.dir\cmake_clean.cmake
.PHONY : animal/CMakeFiles/animalLib.dir/clean

animal/CMakeFiles/animalLib.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal\CMakeFiles\animalLib.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : animal/CMakeFiles/animalLib.dir/depend

