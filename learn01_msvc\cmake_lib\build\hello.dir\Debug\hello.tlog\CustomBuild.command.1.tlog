^D:\CPP_WORKSPACE\CMAKE_LEARN\LEARN01_MSVC\CMAKE_LIB\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib -BD:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build --check-stamp-file D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
