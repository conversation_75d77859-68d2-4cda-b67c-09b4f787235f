"C:\Program Files\CMake\bin\cmake.exe" -E rm -f CMakeFiles\hello.dir/objects.a
D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\ar.exe qc CMakeFiles\hello.dir/objects.a @CMakeFiles\hello.dir\objects1.rsp
D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe -Wl,--whole-archive CMakeFiles\hello.dir/objects.a -Wl,--no-whole-archive -o hello.exe -Wl,--out-implib,libhello.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\hello.dir\linkLibs.rsp
