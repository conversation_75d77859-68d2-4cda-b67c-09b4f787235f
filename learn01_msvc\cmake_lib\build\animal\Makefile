# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(CMAKE_COMMAND) -E cmake_progress_start D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal\\CMakeFiles\progress.marks
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 animal/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 animal/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 animal/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 animal/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
animal/CMakeFiles/animalLib.dir/rule:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 animal/CMakeFiles/animalLib.dir/rule
.PHONY : animal/CMakeFiles/animalLib.dir/rule

# Convenience name for target.
animalLib: animal/CMakeFiles/animalLib.dir/rule
.PHONY : animalLib

# fast build rule for target.
animalLib/fast:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/build
.PHONY : animalLib/fast

cat.obj: cat.cpp.obj
.PHONY : cat.obj

# target to build an object file
cat.cpp.obj:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/cat.cpp.obj
.PHONY : cat.cpp.obj

cat.i: cat.cpp.i
.PHONY : cat.i

# target to preprocess a source file
cat.cpp.i:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/cat.cpp.i
.PHONY : cat.cpp.i

cat.s: cat.cpp.s
.PHONY : cat.s

# target to generate assembly for a file
cat.cpp.s:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/cat.cpp.s
.PHONY : cat.cpp.s

dog.obj: dog.cpp.obj
.PHONY : dog.obj

# target to build an object file
dog.cpp.obj:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/dog.cpp.obj
.PHONY : dog.cpp.obj

dog.i: dog.cpp.i
.PHONY : dog.i

# target to preprocess a source file
dog.cpp.i:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/dog.cpp.i
.PHONY : dog.cpp.i

dog.s: dog.cpp.s
.PHONY : dog.s

# target to generate assembly for a file
dog.cpp.s:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(MAKE) $(MAKESILENT) -f animal\CMakeFiles\animalLib.dir\build.make animal/CMakeFiles/animalLib.dir/dog.cpp.s
.PHONY : dog.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... animalLib
	@echo ... cat.obj
	@echo ... cat.i
	@echo ... cat.s
	@echo ... dog.obj
	@echo ... dog.i
	@echo ... dog.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

