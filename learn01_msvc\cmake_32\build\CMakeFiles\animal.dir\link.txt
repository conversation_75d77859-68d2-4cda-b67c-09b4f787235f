"C:\Program Files\CMake\bin\cmake.exe" -E rm -f CMakeFiles\animal.dir/objects.a
D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\ar.exe qc CMakeFiles\animal.dir/objects.a @CMakeFiles\animal.dir\objects1.rsp
D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe -Wl,--whole-archive CMakeFiles\animal.dir/objects.a -Wl,--no-whole-archive -o animal.exe -Wl,--out-implib,libanimal.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\animal.dir\linkLibs.rsp
