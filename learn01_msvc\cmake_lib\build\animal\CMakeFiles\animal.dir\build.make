# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build

# Include any dependencies generated for this target.
include animal/CMakeFiles/animal.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include animal/CMakeFiles/animal.dir/compiler_depend.make

# Include the progress variables for this target.
include animal/CMakeFiles/animal.dir/progress.make

# Include the compile flags for this target's objects.
include animal/CMakeFiles/animal.dir/flags.make

animal/CMakeFiles/animal.dir/codegen:
.PHONY : animal/CMakeFiles/animal.dir/codegen

animal/CMakeFiles/animal.dir/dog.cpp.obj: animal/CMakeFiles/animal.dir/flags.make
animal/CMakeFiles/animal.dir/dog.cpp.obj: D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/animal/dog.cpp
animal/CMakeFiles/animal.dir/dog.cpp.obj: animal/CMakeFiles/animal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object animal/CMakeFiles/animal.dir/dog.cpp.obj"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT animal/CMakeFiles/animal.dir/dog.cpp.obj -MF CMakeFiles\animal.dir\dog.cpp.obj.d -o CMakeFiles\animal.dir\dog.cpp.obj -c D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\dog.cpp

animal/CMakeFiles/animal.dir/dog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/animal.dir/dog.cpp.i"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\dog.cpp > CMakeFiles\animal.dir\dog.cpp.i

animal/CMakeFiles/animal.dir/dog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/animal.dir/dog.cpp.s"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\dog.cpp -o CMakeFiles\animal.dir\dog.cpp.s

animal/CMakeFiles/animal.dir/cat.cpp.obj: animal/CMakeFiles/animal.dir/flags.make
animal/CMakeFiles/animal.dir/cat.cpp.obj: D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/animal/cat.cpp
animal/CMakeFiles/animal.dir/cat.cpp.obj: animal/CMakeFiles/animal.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object animal/CMakeFiles/animal.dir/cat.cpp.obj"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT animal/CMakeFiles/animal.dir/cat.cpp.obj -MF CMakeFiles\animal.dir\cat.cpp.obj.d -o CMakeFiles\animal.dir\cat.cpp.obj -c D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\cat.cpp

animal/CMakeFiles/animal.dir/cat.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/animal.dir/cat.cpp.i"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\cat.cpp > CMakeFiles\animal.dir\cat.cpp.i

animal/CMakeFiles/animal.dir/cat.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/animal.dir/cat.cpp.s"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && D:\Qt\Qt5.6.3\Tools\mingw492_32\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal\cat.cpp -o CMakeFiles\animal.dir\cat.cpp.s

# Object files for target animal
animal_OBJECTS = \
"CMakeFiles/animal.dir/dog.cpp.obj" \
"CMakeFiles/animal.dir/cat.cpp.obj"

# External object files for target animal
animal_EXTERNAL_OBJECTS =

animal/libanimal.a: animal/CMakeFiles/animal.dir/dog.cpp.obj
animal/libanimal.a: animal/CMakeFiles/animal.dir/cat.cpp.obj
animal/libanimal.a: animal/CMakeFiles/animal.dir/build.make
animal/libanimal.a: animal/CMakeFiles/animal.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libanimal.a"
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && $(CMAKE_COMMAND) -P CMakeFiles\animal.dir\cmake_clean_target.cmake
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\animal.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
animal/CMakeFiles/animal.dir/build: animal/libanimal.a
.PHONY : animal/CMakeFiles/animal.dir/build

animal/CMakeFiles/animal.dir/clean:
	cd /d D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal && $(CMAKE_COMMAND) -P CMakeFiles\animal.dir\cmake_clean.cmake
.PHONY : animal/CMakeFiles/animal.dir/clean

animal/CMakeFiles/animal.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\animal D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal D:\cpp_workspace\cmake_learn\learn01_msvc\cmake_lib\build\animal\CMakeFiles\animal.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : animal/CMakeFiles/animal.dir/depend

