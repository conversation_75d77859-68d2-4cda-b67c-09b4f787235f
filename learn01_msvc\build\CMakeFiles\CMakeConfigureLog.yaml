
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      1
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/8/14 9:14:59。
      节点 1 上的项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
      C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\MSBuild\\Microsoft\\VC\\v160\\Microsoft.CppBuild.targets(495,5): warning MSB8003: 未定义 WindowsSDKDir 属性。可能找不到某些生成工具。 [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj]
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
      LINK : fatal error LNK1181: 无法打开输入文件“kernel32.lib” [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj]
      已完成生成项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作 - 失败。
      
      生成失败。
      
      “D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标) (1) ->
      (PrepareForBuild 目标) -> 
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\MSBuild\\Microsoft\\VC\\v160\\Microsoft.CppBuild.targets(495,5): warning MSB8003: 未定义 WindowsSDKDir 属性。可能找不到某些生成工具。 [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj]
      
      
      “D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标) (1) ->
      (Link 目标) -> 
        LINK : fatal error LNK1181: 无法打开输入文件“kernel32.lib” [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj]
      
          1 个警告
          1 个错误
      
      已用时间 00:00:00.59
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      1
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/8/14 9:15:00。
      节点 1 上的项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
      C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\MSBuild\\Microsoft\\VC\\v160\\Microsoft.CppBuild.targets(495,5): warning MSB8003: 未定义 WindowsSDKDir 属性。可能找不到某些生成工具。 [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj]
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
      LINK : fatal error LNK1181: 无法打开输入文件“kernel32.lib” [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj]
      已完成生成项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作 - 失败。
      
      生成失败。
      
      “D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标) (1) ->
      (PrepareForBuild 目标) -> 
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\MSBuild\\Microsoft\\VC\\v160\\Microsoft.CppBuild.targets(495,5): warning MSB8003: 未定义 WindowsSDKDir 属性。可能找不到某些生成工具。 [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj]
      
      
      “D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标) (1) ->
      (Link 目标) -> 
        LINK : fatal error LNK1181: 无法打开输入文件“kernel32.lib” [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdC\\CompilerIdC.vcxproj]
      
          1 个警告
          1 个错误
      
      已用时间 00:00:00.44
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      1
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/8/14 9:15:01。
      节点 1 上的项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
      C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\MSBuild\\Microsoft\\VC\\v160\\Microsoft.CppBuild.targets(495,5): warning MSB8003: 未定义 WindowsSDKDir 属性。可能找不到某些生成工具。 [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
      LINK : fatal error LNK1181: 无法打开输入文件“kernel32.lib” [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      已完成生成项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作 - 失败。
      
      生成失败。
      
      “D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标) (1) ->
      (PrepareForBuild 目标) -> 
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\MSBuild\\Microsoft\\VC\\v160\\Microsoft.CppBuild.targets(495,5): warning MSB8003: 未定义 WindowsSDKDir 属性。可能找不到某些生成工具。 [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      
      
      “D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标) (1) ->
      (Link 目标) -> 
        LINK : fatal error LNK1181: 无法打开输入文件“kernel32.lib” [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      
          1 个警告
          1 个错误
      
      已用时间 00:00:00.44
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      1
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/8/14 9:15:01。
      节点 1 上的项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
      C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\MSBuild\\Microsoft\\VC\\v160\\Microsoft.CppBuild.targets(495,5): warning MSB8003: 未定义 WindowsSDKDir 属性。可能找不到某些生成工具。 [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
      LINK : fatal error LNK1181: 无法打开输入文件“kernel32.lib” [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      已完成生成项目“D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作 - 失败。
      
      生成失败。
      
      “D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标) (1) ->
      (PrepareForBuild 目标) -> 
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\MSBuild\\Microsoft\\VC\\v160\\Microsoft.CppBuild.targets(495,5): warning MSB8003: 未定义 WindowsSDKDir 属性。可能找不到某些生成工具。 [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      
      
      “D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标) (1) ->
      (Link 目标) -> 
        LINK : fatal error LNK1181: 无法打开输入文件“kernel32.lib” [D:\\cpp_workspace\\cmake_learn\\learn01_msvc\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      
          1 个警告
          1 个错误
      
      已用时间 00:00:00.45
      
      
...
