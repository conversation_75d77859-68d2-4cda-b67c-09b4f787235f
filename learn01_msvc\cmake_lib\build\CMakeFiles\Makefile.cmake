# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXCompiler.cmake.in"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCompilerIdDetection.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerSupport.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineRCCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindBinUtils.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeMinGWFindMake.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeParseImplicitLinkInfo.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeParseLibraryArchitecture.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeRCCompiler.cmake.in"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeRCInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystem.cmake.in"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCompilerCommon.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestRCCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GNU-CXX-FeatureTests.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GNU-FindBinUtils.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/TI-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/FeatureTesting.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Linker/GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Linker/GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-Determine-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-GNU.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-windres.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows.cmake"
  "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake"
  "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/CMakeLists.txt"
  "D:/cpp_workspace/cmake_learn/learn01_msvc/cmake_lib/animal/CMakeLists.txt"
  "CMakeFiles/3.31.5/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.31.5/CMakeRCCompiler.cmake"
  "CMakeFiles/3.31.5/CMakeSystem.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.31.5/CMakeSystem.cmake"
  "CMakeFiles/3.31.5/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.31.5/CMakeRCCompiler.cmake"
  "CMakeFiles/3.31.5/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "animal/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/hello.dir/DependInfo.cmake"
  "animal/CMakeFiles/animalLib.dir/DependInfo.cmake"
  )
